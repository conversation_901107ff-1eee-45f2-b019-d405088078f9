
import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, Subject, throwError } from 'rxjs';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { ITEMS_PER_PAGE, SoftwareBuildListResource } from '../../../app.constants';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { SoftwareBuildSearchRequestBody } from '../../../model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { SoftwareBuildFilterAction } from '../../../model/SoftwaarBuilds/SoftwareFilterAction.model';
import { DownloadJsonResponse, SubTitleInformation, VideoInformation } from '../../../model/video/download-json-response.model';
import { Jsonlist } from '../../../model/video/jsonlist.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { PermissionService } from '../../../shared/permission.service';
import { HideShowEditIconForSoftwareBuildePipe } from '../../../shared/pipes/HideShowEditIconForSoftwareBuildePipe.pipe';
import { JsonNamePipe } from '../../../shared/pipes/json-name.pipe';
import { ModelDisplayNameListToStringConvert } from '../../../shared/pipes/ModelDisplayNameListToStringConvert.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { SoftwareBuildMappedDevicePipe } from '../../../shared/pipes/Software Build/software-build-mapped-device.pipe';
import { SoftwareBuildStatusPipe } from '../../../shared/pipes/Software Build/software-build-status.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { UploadScanService } from '../../../shared/upload-scan.service';
import { CommonCheckboxService } from '../../../shared/util/common-checkbox.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { VideoService } from '../../../shared/videoservice/video.service';
import { commonsProviders, countryListResponse } from '../../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';
import { EditSoftwareBuildComponent } from '../Edit-Software-Build/edit-software-build.component';
import { SoftwareBuildConfirmComponent } from '../Software-build-confirm/software-build-confirm.component';
import { SoftwareBuildFilterComponent } from '../software-build-filter/software-build-filter.component';
import { EditSoftwareBuildDialogService } from '../software-build-services/edit-software-build-dialog/edit-software-build-dialog.service';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { UploadSoftwareBuildDialogComponent } from '../upload-software-build-dialog/upload-software-build-dialog.component';
import { SoftwareBuildListComponent } from './software-build-list.component';

describe('SoftwareBuildListComponent', () => {
  let component: SoftwareBuildListComponent;
  let fixture: ComponentFixture<SoftwareBuildListComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let softwareBuildApiCallServiceMock: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let videoServiceSpy: jasmine.SpyObj<VideoService>;
  let moduleValidationServiceService: ModuleValidationServiceService;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let softwareBuildOperationsServiceSpy: jasmine.SpyObj<SoftwareBuildOperationsService>;
  let editSoftwareBuildDialogServiceSpy: jasmine.SpyObj<EditSoftwareBuildDialogService>;
  let commonCheckboxServiceSpy: jasmine.SpyObj<CommonCheckboxService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let uploadScanServiceSpy: jasmine.SpyObj<UploadScanService>;

  let inventoryListResponse = {
    "content": [{
      "id": 78,
      "version": "21.22.11",
      "title": "rajT201",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566757199,
      "isActive": true,
      "countries": ["Argentina"],
      "deviceTypes": []
    }, {
      "id": 77,
      "version": "21.22.11",
      "title": "rajT20",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566495667,
      "isActive": true,
      "countries": ["Akshay443"],
      "deviceTypes": []
    }, {
      "id": 76,
      "version": "20.22.11",
      "title": "test",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566194841,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 73,
      "version": "8.0.1",
      "title": "80170",
      "attachmentName": "thor-ota-********.zip",
      "releaseNoteName": "2.3.json",
      "jsonMaster": {
        "id": 1,
        "version": "2.3"
      },
      "partNumber": "EN",
      "createdDate": 1733485708685,
      "isActive": true,
      "countries": ["India"],
      "deviceTypes": ["CLIENT"]
    }, {
      "id": 71,
      "version": "7.2.5",
      "title": "DemoTestin121",
      "attachmentName": "Final 1 (2) (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733382706002,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 70,
      "version": "1.2.3",
      "title": "Demo1q32",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P006245-009",
      "createdDate": 1733381445732,
      "isActive": true,
      "countries": ["Bosnia and Herzegovina"],
      "deviceTypes": []
    }, {
      "id": 69,
      "version": "7.2.5",
      "title": "Demo12313",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733380870026,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 68,
      "version": "1.2.3",
      "title": "Testing12emo",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733379217893,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 67,
      "version": "1.2.3",
      "title": "demo1233",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 13,
        "version": "1.2.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733372843029,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 66,
      "version": "1.7.3",
      "title": "AkshayDemo13",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733317463066,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 6,
    "totalElements": 53,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  let videoJosnResponse = [{
    "id": 12,
    "version": "1.1.1"
  }, {
    "id": 13,
    "version": "1.2.1"
  }, {
    "id": 15,
    "version": "1.2.1"
  }, {
    "id": 11,
    "version": "2.1.1"
  }, {
    "id": 19,
    "version": "2.1.3"
  }, {
    "id": 1,
    "version": "2.3"
  }, {
    "id": 21,
    "version": "20.20.20"
  }, {
    "id": 20,
    "version": "test"
  }, {
    "id": 16,
    "version": "Video3_v2-3"
  }]

  const jsonVersion: DownloadJsonResponse = new DownloadJsonResponse(
    "2.3.4",
    [new VideoInformation(
      "sajkl",
      "0:00",
      1234567,
      [new SubTitleInformation("enxsd", "ddccsr")]
    )]
  );
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getSoftwearBuildPermission']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache', 'filterOutUserAssociatedCountries']);
    softwareBuildApiCallServiceMock = jasmine.createSpyObj('InventoryService', ['inventoryList', 'mapInventoryWithDeviceType', 'markInventoriesActiveInactive', 'getAttachmentUrl', 'deleteSoftwearBuild', 'updateInventory', 'pushFileToStorage', 'uploadFileToStorage']);
    downloadService = jasmine.createSpyObj('DownloadService', ['downloadMyFile', 'getisLoadingSubject', 'setLoading']);
    videoServiceSpy = jasmine.createSpyObj('VideoService', ['getListofJsonVersions', 'downloadJSONFile']);
    softwareBuildOperationsServiceSpy = jasmine.createSpyObj('SoftwareBuildOperationsService', [
      'getSoftwareBuildListLoadingSubject', 'getSoftwareBuildListFilterRequestParameterSubject',
      'getSoftwareBuildListRefreshSubject', 'getJsonVersionList', 'setJsonVersionList', 'getCountryList', 'setCountryList',
      'loadSoftwareBuildList', 'downloadAttachment', 'deleteSoftwareBuild', 'changeOperationForSoftwareBuild',
      'callRefreshPageSubject', 'downloadJSONFile', 'callSoftwareBuildListFilterRequestParameterSubject',
      'setSoftwareBuildSearchRequestBodyForListingApi', 'getSoftwareBuildSearchRequestBodyForListingApi',
      'setIsFilterHiddenForListing', 'getIsFilterHiddenForListing', 'setListPageRefreshForbackToOtherPage', 'getListPageRefreshForbackToOtherPage'
    ]);
    editSoftwareBuildDialogServiceSpy = jasmine.createSpyObj('EditSoftwareBuildDialogService', ['openEditInventoryModel']);
    commonCheckboxServiceSpy = jasmine.createSpyObj('CommonCheckboxService', ['defaultSelectAll', 'selectAllItem', 'clearSelectAllCheckbox']);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['accessSoftwareBuildOperations']);
    uploadScanServiceSpy = jasmine.createSpyObj('UploadScanService', ['confirm']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue(['Algeria', 'Argentina', 'Australia', 'Austria', 'Belgium', 'india']);

    // Setup default mock returns
    authServiceSpy.isAuthenticate.and.returnValue(true);
    permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
    countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(countryListResponse));
    downloadService.getisLoadingSubject.and.returnValue(new Subject<boolean>());
    videoServiceSpy.getListofJsonVersions.and.returnValue(of(new HttpResponse({ body: videoJosnResponse, status: 200 })));
    softwareBuildOperationsServiceSpy.getSoftwareBuildListLoadingSubject.and.returnValue(new Subject<boolean>());
    softwareBuildOperationsServiceSpy.getSoftwareBuildListFilterRequestParameterSubject.and.returnValue(new Subject<SoftwareBuildFilterAction>());
    softwareBuildOperationsServiceSpy.getSoftwareBuildListRefreshSubject.and.returnValue(new Subject<any>());
    softwareBuildOperationsServiceSpy.getJsonVersionList.and.returnValue([]);
    softwareBuildOperationsServiceSpy.getCountryList.and.returnValue([]);
    softwareBuildOperationsServiceSpy.getSoftwareBuildSearchRequestBodyForListingApi.and.returnValue(null);
    softwareBuildOperationsServiceSpy.getIsFilterHiddenForListing.and.returnValue(false);
    softwareBuildOperationsServiceSpy.getListPageRefreshForbackToOtherPage.and.returnValue(false);
    commonOperationsServiceSpy.accessSoftwareBuildOperations.and.returnValue(['Map to Client Devices', 'Map to Demo Devices']);
    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildListComponent, SoftwareBuildFilterComponent, JsonNamePipe, ModelDisplayNameListToStringConvert, SoftwareBuildMappedDevicePipe, SoftwareBuildStatusPipe, HideShowEditIconForSoftwareBuildePipe, SoftwareBuildConfirmComponent, EditSoftwareBuildComponent, UploadSoftwareBuildDialogComponent],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceMock },
        { provide: DownloadService, useValue: downloadService },
        { provide: VideoService, useValue: videoServiceSpy },
        { provide: SoftwareBuildOperationsService, useValue: softwareBuildOperationsServiceSpy },
        { provide: EditSoftwareBuildDialogService, useValue: editSoftwareBuildDialogServiceSpy },
        { provide: CommonCheckboxService, useValue: commonCheckboxServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: UploadScanService, useValue: uploadScanServiceSpy },
        DatePipe,
        PrintListPipe,
        ExceptionHandlingService,
        SessionStorageService,
        CommonsService,
        ConfirmDialogService,
        SSOLoginService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    // Mock API call to fetch the Role list and return a successful response
    softwareBuildApiCallServiceMock.inventoryList?.and.returnValue(of(new HttpResponse<any>({
      body: inventoryListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== AUTHENTICATION TESTS ====================

  describe('Authentication', () => {
    it('should navigate to login when user is not authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(false);

      component.ngOnInit();

      expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
    });

    it('should initialize component when user is authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      spyOn(component, 'setItemInventoryPermission' as any);
      spyOn(component, 'loadCachedData' as any);
      spyOn(component, 'refreshFilter');

      component.ngOnInit();

      expect(component.page).toBe(0);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.isFilterHidden).toBe(false);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component['setItemInventoryPermission']).toHaveBeenCalled();
      expect(component['loadCachedData']).toHaveBeenCalled();
      expect(component.refreshFilter).toHaveBeenCalled();
    });
  });

  // ==================== PERMISSION TESTS ====================

  describe('Permissions', () => {
    it('should set software build permissions correctly', () => {
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValues(true, false, true);

      component['setItemInventoryPermission']();

      expect(component.deleteSoftwareBuildPermission).toBe(true);
      expect(component.uploadSoftwareBuildPermission).toBe(false);
      expect(component.updateSoftwareBuildPermission).toBe(true);
      expect(permissionServiceSpy.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.DELETE_SOFTWARE_BUILD_ACTION);
      expect(permissionServiceSpy.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.UPLOAD_SOFTWARE_BUILD_ACTION);
      expect(permissionServiceSpy.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION);
    });
  });

  // ==================== CACHED DATA TESTS ====================

  describe('Cached Data Loading', () => {
    it('should load JSON version when cache is empty', async () => {
      softwareBuildOperationsServiceSpy.getJsonVersionList.and.returnValue([]);
      spyOn(component, 'getJsonVersion' as any);

      await component['loadCachedData']();

      expect(component['getJsonVersion']).toHaveBeenCalled();
    });

    it('should use cached JSON version when available', async () => {
      const cachedJsonList = [new Jsonlist(1, 'v1.0')];
      softwareBuildOperationsServiceSpy.getJsonVersionList.and.returnValue(cachedJsonList);

      await component['loadCachedData']();

      expect(component.jsonVersionList).toEqual(cachedJsonList);
    });

    it('should load country list when cache is empty', async () => {
      softwareBuildOperationsServiceSpy.getCountryList.and.returnValue([]);
      spyOn(component, 'getCountryList' as any);

      await component['loadCachedData']();

      expect(component['getCountryList']).toHaveBeenCalled();
    });

    it('should use cached country list when available', async () => {
      const cachedCountryList = countryListResponse;
      softwareBuildOperationsServiceSpy.getCountryList.and.returnValue(cachedCountryList);

      await component['loadCachedData']();

      expect(component.userAssociatedCountrys).toEqual(cachedCountryList);
    });
  });

  // ==================== JSON VERSION TESTS ====================

  describe('JSON Version Loading', () => {
    it('should get JSON version successfully', async () => {
      const mockResponse = new HttpResponse({ body: videoJosnResponse, status: 200 });
      videoServiceSpy.getListofJsonVersions.and.returnValue(of(mockResponse));
      spyOn(component['commonsService'], 'checkForNull').and.returnValue(videoJosnResponse);

      await component['getJsonVersion']();

      expect(component.jsonVersionList).toEqual(videoJosnResponse);
      expect(softwareBuildOperationsServiceSpy.setJsonVersionList).toHaveBeenCalledWith(videoJosnResponse);
    });

    it('should handle JSON version loading error', async () => {
      const mockError = new HttpErrorResponse({ status: 500 });
      videoServiceSpy.getListofJsonVersions.and.returnValue(throwError(() => mockError));
      spyOn(component['exceptionService'], 'customErrorMessage');

      await component['getJsonVersion']();

      expect(component['exceptionService'].customErrorMessage).toHaveBeenCalledWith(mockError);
    });
  });

  // ==================== COUNTRY LIST TESTS ====================

  describe('Country List Loading', () => {
    it('should get country list successfully', async () => {
      const mockCountryList = countryListResponse;
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountryList));

      await component['getCountryList']();

      expect(component.userAssociatedCountrys).toEqual(mockCountryList);
      expect(softwareBuildOperationsServiceSpy.setCountryList).toHaveBeenCalledWith(mockCountryList);
    });
  });

  // ==================== FILTER AND REFRESH TESTS ====================

  describe('Filter and Refresh Operations', () => {
    it('should call refresh page subject with correct parameters', () => {
      component.isFilterHidden = false;
      component.softwareBuildSearchRequestBody = new SoftwareBuildSearchRequestBody('test', [], null, null, null, null);

      component.filterPageSubjectCallForReloadPage(true, false);

      expect(softwareBuildOperationsServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.any(ListingPageReloadSubjectParameter),
        SoftwareBuildListResource,
        false,
        component.softwareBuildSearchRequestBody
      );
    });

    it('should refresh filter and reset page', () => {
      spyOn(component, 'resetPage' as any);
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.refreshFilter();

      expect(component['resetPage']).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, true);
    });

    it('should reset page correctly', () => {
      component.page = 5;
      component.previousPage = 3;

      component['resetPage']();

      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });

    it('should handle refresh button click', () => {
      spyOn(component, 'getCountryList' as any);
      spyOn(component, 'getJsonVersion' as any);
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.clickOnRefreshButton();

      expect(component.page).toBe(0);
      expect(component['getCountryList']).toHaveBeenCalled();
      expect(component['getJsonVersion']).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });
  });

  // ==================== DATA SIZE AND PAGINATION TESTS ====================

  describe('Data Size and Pagination', () => {
    it('should change data size and reload items', () => {
      const mockEvent = { target: { value: 20 } };
      spyOn(component, 'reloadItem');

      component.changeDataSize(mockEvent);

      expect(component.loading).toBe(true);
      expect(component.itemsPerPage).toBe(20);
      expect(component.reloadItem).toHaveBeenCalled();
    });

    it('should reload items and reset page', () => {
      component.inventoryIdList = [1, 2, 3];
      component.page = 5;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.reloadItem();

      expect(component.inventoryIdList).toEqual([]);
      expect(component.page).toBe(0);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
    });

    it('should load page when different from previous page', () => {
      component.previousPage = 1;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loadPage(2);

      expect(component.previousPage).toBe(2);
      expect(commonCheckboxServiceSpy.clearSelectAllCheckbox).toHaveBeenCalledWith(component.selectAllCheckboxId);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
    });

    it('should not load page when same as previous page', () => {
      component.previousPage = 2;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loadPage(2);

      expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
    });
  });

  // ==================== LOAD ALL TESTS ====================

  describe('Load All Software Builds', () => {
    it('should load software builds successfully', async () => {
      const mockSearchBody = new SoftwareBuildSearchRequestBody('test', [], null, null, null, null);
      const mockResult = {
        success: true,
        softwareBuilds: inventoryListResponse.content,
        totalSoftwareBuildDisplay: 10,
        totalSoftwareBuilds: 53,
        localSoftwareBuildList: inventoryListResponse.content,
        totalItems: 53,
        page: 1
      };

      softwareBuildOperationsServiceSpy.loadSoftwareBuildList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'defaultSelectAll' as any);

      await component.loadAll(mockSearchBody);

      expect(component.loading).toBe(false);
      expect(component.inventory).toEqual(jasmine.any(Array));
      expect(component.totalItemInventoryDisplay).toBe(10);
      expect(component.totalItemInventory).toBe(53);
      expect(component['defaultSelectAll']).toHaveBeenCalled();
    });

    it('should handle load failure', async () => {
      const mockSearchBody = new SoftwareBuildSearchRequestBody('test', [], null, null, null, null);
      const mockResult = {
        success: false,
        softwareBuilds: [],
        totalSoftwareBuildDisplay: 0,
        totalSoftwareBuilds: 0,
        localSoftwareBuildList: [],
        totalItems: 0,
        page: 0
      };

      softwareBuildOperationsServiceSpy.loadSoftwareBuildList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'defaultSelectAll' as any);

      await component.loadAll(mockSearchBody);

      expect(component.inventory).toEqual([]);
      expect(component.totalItemInventoryDisplay).toBe(0);
      expect(component.totalItemInventory).toBe(0);
      expect(component.localInventoryList).toEqual([]);
      expect(component.totalItems).toBe(0);
    });
  });

  // ==================== OPERATION TESTS ====================

  describe('Software Build Operations', () => {
    it('should change inventory operation', () => {
      const mockEvent = { target: { value: 'Map to Client Devices' } };
      component.inventoryIdList = [1, 2];
      component.localInventoryList = inventoryListResponse.content as any;
      spyOn(document, 'getElementById').and.returnValue({ value: '' } as any);

      component.changeInventoryOperation(mockEvent);

      expect(softwareBuildOperationsServiceSpy.changeOperationForSoftwareBuild).toHaveBeenCalledWith(
        'Map to Client Devices',
        SoftwareBuildListResource,
        [1, 2],
        jasmine.any(Array)
      );
    });

    it('should get selected software builds correctly', () => {
      component.inventoryIdList = [78, 77];
      component.localInventoryList = inventoryListResponse.content as any;

      const result = component['getSelectedSoftwareBuilds']();

      expect(result.length).toBe(2);
      expect(result[0].id).toBe(78);
      expect(result[1].id).toBe(77);
    });
  });

  // ==================== CHECKBOX TESTS ====================

  describe('Checkbox Operations', () => {
    it('should handle individual checkbox change - checked', () => {
      const mockEvent = { target: { checked: true } };
      spyOn(component, 'defaultSelectAll' as any);

      component.onChangeInventory(1, ['USA'], mockEvent);

      expect(component.inventoryIdList).toContain(1);
      expect(component.seletedinventoryIdWithCountry.get(1)).toEqual(['USA']);
      expect(component['defaultSelectAll']).toHaveBeenCalled();
    });

    it('should handle individual checkbox change - unchecked', () => {
      component.inventoryIdList = [1, 2, 3];
      component.seletedinventoryIdWithCountry.set(2, ['USA']);
      const mockEvent = { target: { checked: false } };
      spyOn(component, 'defaultSelectAll' as any);

      component.onChangeInventory(2, ['USA'], mockEvent);

      expect(component.inventoryIdList).not.toContain(2);
      expect(component.seletedinventoryIdWithCountry.has(2)).toBe(false);
      expect(component['defaultSelectAll']).toHaveBeenCalled();
    });

    it('should handle select all items - checked', () => {
      component.localInventoryList = inventoryListResponse.content as any;
      commonCheckboxServiceSpy.selectAllItem.and.returnValue([78, 77, 76]);

      component.selectAllItem(true);

      expect(component.inventoryIdList).toEqual([78, 77, 76]);
      expect(component.seletedinventoryIdWithCountry.size).toBeGreaterThan(0);
    });

    it('should handle select all items - unchecked', () => {
      component.localInventoryList = inventoryListResponse.content as any;
      component.seletedinventoryIdWithCountry.set(78, ['Argentina']);
      commonCheckboxServiceSpy.selectAllItem.and.returnValue([]);

      component.selectAllItem(false);

      expect(component.inventoryIdList).toEqual([]);
      expect(component.seletedinventoryIdWithCountry.size).toBe(0);
    });

    it('should clear software build selections', () => {
      component.inventoryIdList = [1, 2, 3];
      component.seletedinventoryIdWithCountry.set(1, ['USA']);

      // Mock DOM elements
      const mockCheckboxes = [
        { checked: true },
        { checked: true }
      ];
      const mockSelectAllCheckbox = { checked: true };

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      component.clearSoftwareBuildSelections();

      expect(component.inventoryIdList).toEqual([]);
      expect(component.seletedinventoryIdWithCountry.size).toBe(0);
      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
      expect(mockSelectAllCheckbox.checked).toBe(false);
    });

    it('should handle null select all checkbox in clear selections', () => {
      spyOn(document, 'getElementsByName').and.returnValue([] as any);
      spyOn(document, 'getElementById').and.returnValue(null);

      expect(() => component.clearSoftwareBuildSelections()).not.toThrow();
    });

    it('should call default select all', () => {
      component.localInventoryList = inventoryListResponse.content as any;
      component.inventoryIdList = [78, 77];

      component['defaultSelectAll']();

      expect(commonCheckboxServiceSpy.defaultSelectAll).toHaveBeenCalledWith(
        jasmine.any(Array),
        [78, 77],
        component.selectAllCheckboxId
      );
    });
  });

  // ==================== DOWNLOAD TESTS ====================

  describe('Download Operations', () => {
    it('should get attachment URL and download', async () => {
      const mockUrl = 'https://example.com/attachment.zip';
      softwareBuildOperationsServiceSpy.downloadAttachment.and.returnValue(Promise.resolve(mockUrl));
      spyOn(component, 'downloadMyFileAsync');

      await component.getAttachmentUrl(1);

      expect(softwareBuildOperationsServiceSpy.downloadAttachment).toHaveBeenCalledWith(1, 'attachment', SoftwareBuildListResource);
      expect(component.downloadMyFileAsync).toHaveBeenCalledWith(mockUrl);
    });

    it('should get release note URL and download', async () => {
      const mockUrl = 'https://example.com/release-note.pdf';
      softwareBuildOperationsServiceSpy.downloadAttachment.and.returnValue(Promise.resolve(mockUrl));
      spyOn(component, 'downloadMyFileAsync');

      await component.getReleaseNoteUrl(1);

      expect(softwareBuildOperationsServiceSpy.downloadAttachment).toHaveBeenCalledWith(1, 'releaseNote', SoftwareBuildListResource);
      expect(component.downloadMyFileAsync).toHaveBeenCalledWith(mockUrl);
    });

    it('should download file using download service', async () => {
      const mockUrl = 'https://example.com/file.zip';
      downloadService.downloadMyFile.and.returnValue(Promise.resolve());

      await component.downloadMyFileAsync(mockUrl);

      expect(downloadService.downloadMyFile).toHaveBeenCalledWith(mockUrl);
    });

    it('should download JSON file', async () => {
      const mockJsonMaster = new Jsonlist(1, 'v1.0');
      softwareBuildOperationsServiceSpy.downloadJSONFile.and.returnValue(Promise.resolve());

      await component.downloadJSONFile(mockJsonMaster);

      expect(softwareBuildOperationsServiceSpy.downloadJSONFile).toHaveBeenCalledWith(1, 'v1.0', SoftwareBuildListResource);
    });
  });

  // ==================== DELETE TESTS ====================

  describe('Delete Operations', () => {
    it('should delete software build item', async () => {
      softwareBuildOperationsServiceSpy.deleteSoftwareBuild.and.returnValue(Promise.resolve(true));

      await component.deleteItem(1, 'SW-001');

      expect(softwareBuildOperationsServiceSpy.deleteSoftwareBuild).toHaveBeenCalledWith(1, 'SW-001', SoftwareBuildListResource);
    });
  });

  // ==================== UPLOAD TESTS ====================

  describe('Upload Operations', () => {
    it('should confirm download dataset and reload on confirmation', async () => {
      uploadScanServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      spyOn(component, 'reloadItem');

      await component.confirmDownloadDataset();

      expect(uploadScanServiceSpy.confirm).toHaveBeenCalledWith(
        'Upload Firmware',
        'Please confirm to upload.',
        component.userAssociatedCountrys,
        component.jsonVersionList
      );
      expect(component.loading).toBe(true);
      expect(component.reloadItem).toHaveBeenCalled();
    });

    it('should handle upload cancellation', async () => {
      uploadScanServiceSpy.confirm.and.returnValue(Promise.resolve(false));
      spyOn(component, 'reloadItem');

      await component.confirmDownloadDataset();

      expect(component.loading).toBe(false);
      expect(component.reloadItem).not.toHaveBeenCalled();
    });
  });

  // ==================== TOGGLE FILTER TESTS ====================

  describe('Toggle Filter', () => {
    it('should toggle filter to hidden', () => {
      component.isFilterHidden = false;

      component.toggleFilter();

      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.listPageRefreshForbackToDetailPage).toBe(false);
      expect(component.isFilterHidden).toBe(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.SHOW_FILTER);
    });

    it('should toggle filter to visible', () => {
      component.isFilterHidden = true;

      component.toggleFilter();

      expect(component.isFilterHidden).toBe(false);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
    });
  });

  // ==================== EDIT TESTS ====================

  describe('Edit Operations', () => {
    it('should edit inventory details and reload on confirmation', async () => {
      const mockInventory = inventoryListResponse.content[0] as any;
      editSoftwareBuildDialogServiceSpy.openEditInventoryModel.and.returnValue(Promise.resolve(true));
      spyOn(component, 'reloadItem');

      await component.editInventoryDetails(mockInventory);

      expect(editSoftwareBuildDialogServiceSpy.openEditInventoryModel).toHaveBeenCalledWith(
        jasmine.any(BasicModelConfig),
        mockInventory,
        component.jsonVersionList,
        component.userAssociatedCountrys
      );
      expect(component.reloadItem).toHaveBeenCalled();
    });

    it('should handle edit cancellation', async () => {
      const mockInventory = inventoryListResponse.content[0] as any;
      editSoftwareBuildDialogServiceSpy.openEditInventoryModel.and.returnValue(Promise.resolve(false));
      spyOn(component, 'reloadItem');

      await component.editInventoryDetails(mockInventory);

      expect(component.reloadItem).not.toHaveBeenCalled();
    });
  });

  // ==================== SUBSCRIPTION TESTS ====================

  describe('Subscriptions and Lifecycle', () => {
    it('should initialize subjects correctly', () => {
      const mockLoadingSubject = new Subject<boolean>();
      const mockFilterSubject = new Subject<SoftwareBuildFilterAction>();
      const mockDownloadSubject = new Subject<boolean>();

      softwareBuildOperationsServiceSpy.getSoftwareBuildListLoadingSubject.and.returnValue(mockLoadingSubject);
      softwareBuildOperationsServiceSpy.getSoftwareBuildListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);
      downloadService.getisLoadingSubject.and.returnValue(mockDownloadSubject);

      component['subjectInit']();

      // Test loading subject
      mockLoadingSubject.next(true);
      expect(component.loading).toBe(true);

      // Test download loading subject
      mockDownloadSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle filter subject with reload data', () => {
      const mockFilterSubject = new Subject<SoftwareBuildFilterAction>();
      softwareBuildOperationsServiceSpy.getSoftwareBuildListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);
      spyOn(component, 'loadAll');
      spyOn(component, 'resetPage' as any);

      component['subjectInit']();

      const mockFilterAction = new SoftwareBuildFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        new SoftwareBuildSearchRequestBody('test', [], null, null, null, null)
      );

      mockFilterAction.listingPageReloadSubjectParameter.isReloadData = true;
      mockFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber = true;

      mockFilterSubject.next(mockFilterAction);

      expect(component.inventoryIdList).toEqual([]);
      expect(component.seletedinventoryIdWithCountry.size).toBe(0);
      expect(component['resetPage']).toHaveBeenCalled();
      expect(component.loadAll).toHaveBeenCalledWith(mockFilterAction.softwareBuildSearchRequestBody);
    });

    it('should handle filter subject without default page number', () => {
      const mockFilterSubject = new Subject<SoftwareBuildFilterAction>();
      softwareBuildOperationsServiceSpy.getSoftwareBuildListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);
      spyOn(component, 'loadAll');
      spyOn(component, 'resetPage' as any);

      component['subjectInit']();

      const mockFilterAction = new SoftwareBuildFilterAction(
        new ListingPageReloadSubjectParameter(true, false, false, false),
        new SoftwareBuildSearchRequestBody('test', [], null, null, null, null)
      );

      mockFilterSubject.next(mockFilterAction);

      expect(component['resetPage']).not.toHaveBeenCalled();
      expect(component.loadAll).toHaveBeenCalledWith(mockFilterAction.softwareBuildSearchRequestBody);
    });

    it('should handle filter subject without reload data', () => {
      const mockFilterSubject = new Subject<SoftwareBuildFilterAction>();
      softwareBuildOperationsServiceSpy.getSoftwareBuildListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);
      spyOn(component, 'loadAll');

      component['subjectInit']();

      const mockFilterAction = new SoftwareBuildFilterAction(
        new ListingPageReloadSubjectParameter(false, false, false, false),
        new SoftwareBuildSearchRequestBody('test', [], null, null, null, null)
      );

      mockFilterSubject.next(mockFilterAction);

      expect(component.loadAll).not.toHaveBeenCalled();
    });

    it('should unsubscribe on destroy when subscriptions exist', () => {
      component['subscriptionForLoading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForSoftwareBuildListLoading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);

      component.ngOnDestroy();

      expect(component['subscriptionForLoading'].unsubscribe).toHaveBeenCalled();
      expect(component['subscriptionForSoftwareBuildListLoading'].unsubscribe).toHaveBeenCalled();
    });

    it('should handle destroy when subscriptions are undefined', () => {
      component['subscriptionForLoading'] = undefined;
      component['subscriptionForSoftwareBuildListLoading'] = undefined;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });
});